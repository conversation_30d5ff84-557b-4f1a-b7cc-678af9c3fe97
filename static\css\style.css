/* Trading System Dashboard Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand i {
    margin-right: 8px;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

/* Navigation */
.nav-link {
    color: #6c757d !important;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #007bff !important;
}

.nav-link i {
    margin-right: 5px;
}

/* Pages */
.page {
    display: none;
}

.page.active {
    display: block;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item i {
    font-size: 1.2rem;
    width: 20px;
}

/* Metrics */
.metric {
    margin-bottom: 15px;
}

.metric label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.metric span {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
}

/* Tables */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    font-size: 14px;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
}

/* Strategy Cards */
.strategy-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Badges */
.badge {
    font-size: 11px;
    padding: 5px 8px;
    border-radius: 6px;
}

/* Chart Container */
#chart-container {
    position: relative;
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

#candlestick-chart {
    width: 100%;
    height: 100%;
    min-height: 400px;
}

/* Chart Controls */
.chart-controls {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.chart-controls .form-control,
.chart-controls .form-select {
    font-size: 14px;
}

/* Technical Indicators */
.technical-indicators {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.form-check {
    margin-bottom: 8px;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
}

/* Chart Info */
#chart-info p {
    margin-bottom: 8px;
    font-size: 14px;
}

#chart-info strong {
    color: #495057;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 14px;
    }
    
    .metric span {
        font-size: 16px;
    }
    
    #chart-container {
        padding: 10px;
    }
    
    #candlestick-chart {
        min-height: 300px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .card-header {
        background-color: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table th {
        color: #cccccc;
    }
    
    .form-control,
    .form-select {
        background-color: #404040;
        border-color: #555555;
        color: #ffffff;
    }
    
    .form-control:focus,
    .form-select:focus {
        background-color: #404040;
        color: #ffffff;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tooltip Styles */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background-color: #333;
    border-radius: 4px;
    padding: 8px 12px;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: #dc3545;
}

.status-warning {
    background-color: #ffc107;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Performance Metrics */
.performance-metric {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 15px;
}

.performance-metric.positive {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.performance-metric.negative {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.performance-metric.neutral {
    background-color: #e2e3e5;
    border-left: 4px solid #6c757d;
}

/* Chart Legend */
.chart-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 6px;
}

/* TradingView Chart Customization */
.tv-lightweight-charts {
    border-radius: 8px;
    overflow: hidden;
}

/* Chart Toolbar */
.chart-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 15px;
}

.chart-toolbar .btn-group {
    display: flex;
    gap: 5px;
}

.chart-toolbar .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* Timeframe Selector */
.timeframe-selector {
    display: flex;
    gap: 5px;
}

.timeframe-selector .btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.timeframe-selector .btn.active {
    background-color: #007bff;
    color: white;
}

/* Volume Chart */
.volume-chart {
    height: 100px;
    margin-top: 10px;
}

/* Price Display */
.price-display {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 15px;
}

.price-up {
    color: #28a745;
}

.price-down {
    color: #dc3545;
}

.price-unchanged {
    color: #6c757d;
} 