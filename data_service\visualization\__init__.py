"""
Visualization Module
Provides advanced charting capabilities with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>
"""

try:
    from .plotly_charts import Plotly<PERSON>hartGenerator
    from .matplotlib_charts import MatplotlibChartGenerator
    from .real_time_charts import RealTimeChartManager
    from .dashboard_charts import DashboardChartGenerator
except ImportError as e:
    PlotlyChartGenerator = None
    MatplotlibChartGenerator = None
    RealTimeChartManager = None
    DashboardChartGenerator = None

__all__ = [
    'PlotlyChartGenerator', 
    'MatplotlibChartGenerator', 
    'RealTimeChartManager', 
    'DashboardChartGenerator'
] 