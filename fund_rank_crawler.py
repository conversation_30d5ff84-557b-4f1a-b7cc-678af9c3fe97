import requests
from bs4 import BeautifulSoup
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def fetch_fund_rankings():
    # 配置Selenium
    options = webdriver.ChromeOptions()
    options.add_argument("--headless")  # 无头模式
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    
    # 初始化WebDriver（直接指定ChromeDriver路径）
    driver = webdriver.Chrome(service=Service(r"C:\chromedriver\chromedriver.exe"), options=options)
    
    try:
        # 访问页面
        driver.get("https://fund.eastmoney.com/ztjj/#!syl/Y")
        
        # 等待数据加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#ztTable tbody tr"))
        )
        
        # 解析数据
        fund_data = []
        rows = driver.find_elements(By.CSS_SELECTOR, "#ztTable tbody tr")
        for row in rows[:10]:  # 仅爬取前10条数据
            columns = row.find_elements(By.TAG_NAME, "td")
            if len(columns) >= 4:  # 确保有足够的数据列
                fund_name = columns[1].text.strip()
                fund_code = columns[2].text.strip()
                rankings = [col.text.strip() for col in columns[3:6]]  # 提取近1周、近1月、近3月排名
                if rankings:  # 确保排名数据不为空
                    fund_data.append({"name": fund_name, "code": fund_code, "rankings": rankings})
        
        if not fund_data:
            raise ValueError("未能爬取到有效数据，请检查页面结构或网络连接。")
        
        return fund_data
    finally:
        driver.quit()

    # 实际爬取逻辑（注释掉，后续调试时启用）
    # url = "https://fund.eastmoney.com/data/fundranking.html"
    # headers = {
    #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    # }
    # response = requests.get(url, headers=headers)
    # response.encoding = "utf-8"
    # soup = BeautifulSoup(response.text, "html.parser")
    # 
    # fund_data = []
    # rows = soup.select("#dbtable tbody tr")
    # for row in rows[:10]:
    #     columns = row.find_all("td")
    #     fund_name = columns[1].text.strip()
    #     fund_code = columns[2].text.strip()
    #     rankings = [col.text.strip() for col in columns[3:]]
    #     fund_data.append({"name": fund_name, "code": fund_code, "rankings": rankings})
    # return fund_data

def export_to_excel(data):
    wb = Workbook()
    ws = wb.active
    ws.title = "基金主题排名"
    
    # 设置表头
    headers = ["基金主题名称", "基金主题代码"]
    headers.extend([f"排名_{i+1}" for i in range(len(data[0]["rankings"]))])
    ws.append(headers)
    
    # 填充数据
    for item in data:
        row = [
            item["name"],
            item["code"],
            *item["rankings"]
        ]
        ws.append(row)
    
    # 设置超链接（示例）
    for row in ws.iter_rows(min_row=2, max_row=len(data)+1, min_col=1, max_col=1):
        for cell in row:
            cell.hyperlink = f"https://fund.eastmoney.com/{cell.value}.html"
            cell.font = Font(color="0000FF", underline="single")
    
    # 调整列宽
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2) * 1.2
        ws.column_dimensions[column].width = adjusted_width
    
    # 保存文件
    wb.save("fund_rankings_new.xlsx")
    print("Excel文件已生成：fund_rankings.xlsx")

if __name__ == "__main__":
    fund_data = fetch_fund_rankings()
    export_to_excel(fund_data)